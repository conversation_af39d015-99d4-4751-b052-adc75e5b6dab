<?php
/**
 * 销售数据调试脚本
 * 用于诊断为什么销售统计数据显示为0的问题
 */

// 引入框架
require_once 'vendor/autoload.php';
use think\facade\Db;

// 配置数据库连接
$config = [
    'hostname' => 'localhost',
    'database' => 'qixian_zhongheng',
    'username' => 'root',
    'password' => 'root123',
    'hostport' => '3306',
    'prefix'   => 'ddwx_',
    'charset'  => 'utf8',
];

// 初始化数据库
Db::setConfig($config);

echo "=== 销售数据调试脚本 ===\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n\n";

// 测试参数 - 请根据实际情况修改
$aid = 1; // 应用ID
$test_mid = 1; // 测试用户ID，请修改为实际的用户ID

echo "测试参数: aid={$aid}, mid={$test_mid}\n\n";

try {
    // 1. 检查用户信息
    echo "1. 检查用户信息:\n";
    $userinfo = Db::name('member')->where('aid', $aid)->where('id', $test_mid)->find();
    if ($userinfo) {
        echo "用户ID: {$userinfo['id']}, 昵称: {$userinfo['nickname']}, 等级ID: {$userinfo['levelid']}\n";
    } else {
        echo "❌ 用户不存在！\n";
        exit;
    }
    echo "\n";

    // 2. 检查会员等级数据
    echo "2. 检查会员等级数据:\n";
    $levels = Db::name('member_level')->where('aid', $aid)->select()->toArray();
    echo "总共有 " . count($levels) . " 个等级:\n";
    foreach ($levels as $level) {
        echo "等级ID: {$level['id']}, 名称: {$level['name']}, 排序: {$level['sort']}\n";
    }
    
    // 检查等级ID筛选条件
    $levelids = Db::name('member_level')->where('aid', $aid)->where('id', '>=', 2)->column('id');
    echo "符合条件的等级IDs (id >= 2): " . implode(',', $levelids) . "\n";
    echo "\n";

    // 3. 检查团队成员
    echo "3. 检查团队成员:\n";
    if (empty($levelids)) {
        echo "❌ 没有符合条件的等级ID，这可能是问题所在！\n";
        echo "建议检查：\n";
        echo "- 是否有ID >= 2的等级\n";
        echo "- 等级配置是否正确\n";
    } else {
        // 使用系统方法获取团队成员
        $teamMids = \app\common\Member::getteammids($aid, $test_mid, 999, $levelids);
        echo "团队成员IDs: " . implode(',', $teamMids) . "\n";
        echo "团队成员数量: " . count($teamMids) . "\n";
        
        // 包含自己
        $teamMids[] = $test_mid;
        echo "包含自己后的成员IDs: " . implode(',', $teamMids) . "\n";
        echo "总成员数量: " . count($teamMids) . "\n";
    }
    echo "\n";

    // 4. 检查订单数据
    echo "4. 检查订单数据:\n";
    
    // 检查总订单数量
    $total_orders = Db::name('shop_order_goods')->where('aid', $aid)->count();
    echo "总订单商品记录数: {$total_orders}\n";
    
    if ($total_orders == 0) {
        echo "❌ 数据库中没有任何订单数据！\n";
        echo "建议检查：\n";
        echo "- 是否有用户下过订单\n";
        echo "- 订单数据是否正确导入\n";
        echo "- 表前缀是否正确\n";
    } else {
        // 检查不同状态的订单
        $status_counts = [];
        for ($i = 0; $i <= 5; $i++) {
            $count = Db::name('shop_order_goods')->where('aid', $aid)->where('status', $i)->count();
            if ($count > 0) {
                $status_counts[$i] = $count;
            }
        }
        echo "各状态订单数量: " . json_encode($status_counts) . "\n";
        
        // 检查符合条件的订单（状态1,2,3）
        $valid_orders = Db::name('shop_order_goods')
            ->where('aid', $aid)
            ->where('status', 'in', [1,2,3])
            ->count();
        echo "符合条件的订单数量 (status in 1,2,3): {$valid_orders}\n";
        
        if (!empty($teamMids)) {
            // 检查团队成员的订单
            $team_orders = Db::name('shop_order_goods')
                ->where('aid', $aid)
                ->where('mid', 'in', $teamMids)
                ->where('status', 'in', [1,2,3])
                ->count();
            echo "团队成员的有效订单数量: {$team_orders}\n";
            
            // 计算销售件数
            $sales_count = Db::name('shop_order_goods')
                ->where('aid', $aid)
                ->where('mid', 'in', $teamMids)
                ->where('status', 'in', [1,2,3])
                ->sum('num');
            echo "团队销售件数: {$sales_count}\n";
            
            // 显示最近的几条订单记录
            $recent_orders = Db::name('shop_order_goods')
                ->where('aid', $aid)
                ->where('mid', 'in', $teamMids)
                ->where('status', 'in', [1,2,3])
                ->order('createtime', 'desc')
                ->limit(5)
                ->select()
                ->toArray();
            
            if (!empty($recent_orders)) {
                echo "\n最近的订单记录:\n";
                foreach ($recent_orders as $order) {
                    echo "订单ID: {$order['id']}, 用户ID: {$order['mid']}, 商品: {$order['name']}, 数量: {$order['num']}, 状态: {$order['status']}, 时间: " . date('Y-m-d H:i:s', $order['createtime']) . "\n";
                }
            }
        }
    }
    echo "\n";

    // 5. 检查teamyeji_show配置
    echo "5. 检查系统配置:\n";
    $sysset = Db::name('shop_sysset')->where('aid', $aid)->find();
    if ($sysset) {
        echo "teamyeji_show: " . ($sysset['teamyeji_show'] ?? '未设置') . "\n";
        echo "teamnum_show: " . ($sysset['teamnum_show'] ?? '未设置') . "\n";
    } else {
        echo "❌ 未找到系统配置！\n";
    }
    echo "\n";

    // 6. 总结问题
    echo "6. 问题诊断总结:\n";
    $issues = [];
    
    if (empty($levelids)) {
        $issues[] = "没有符合条件的会员等级 (id >= 2)";
    }
    
    if (empty($teamMids)) {
        $issues[] = "没有团队成员";
    }
    
    if ($total_orders == 0) {
        $issues[] = "数据库中没有订单数据";
    }
    
    if ($valid_orders == 0) {
        $issues[] = "没有符合条件的订单 (status in 1,2,3)";
    }
    
    if (!empty($issues)) {
        echo "❌ 发现以下问题:\n";
        foreach ($issues as $issue) {
            echo "- {$issue}\n";
        }
    } else {
        echo "✅ 基础数据检查正常，可能是其他原因导致的问题\n";
    }

} catch (Exception $e) {
    echo "❌ 执行过程中出错: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}

echo "\n=== 调试完成 ===\n";
echo "请将以上信息提供给开发人员进行进一步分析。\n";
?>
