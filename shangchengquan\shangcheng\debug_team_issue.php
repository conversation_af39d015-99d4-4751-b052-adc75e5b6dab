<?php
/**
 * 团队成员查询问题调试脚本
 * 专门用于调试getteammids返回0的问题
 */

// 引入框架
require_once 'vendor/autoload.php';
use think\facade\Db;

// 配置数据库连接
$config = [
    'hostname' => 'localhost',
    'database' => 'qixian_zhongheng',
    'username' => 'root',
    'password' => 'root123',
    'hostport' => '3306',
    'prefix'   => 'ddwx_',
    'charset'  => 'utf8',
];

// 初始化数据库
Db::setConfig($config);

echo "=== 团队成员查询问题调试脚本 ===\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n\n";

// 从日志中获取的参数
$aid = 110;
$mid = 4500;
$deep = 999;
$levelids = [279,280,287,288,289,290];

echo "调试参数: aid={$aid}, mid={$mid}, deep={$deep}\n";
echo "等级IDs: " . implode(',', $levelids) . "\n\n";

try {
    // 1. 检查用户基本信息
    echo "1. 检查用户基本信息:\n";
    $userinfo = Db::name('member')->where('aid', $aid)->where('id', $mid)->find();
    if ($userinfo) {
        echo "用户ID: {$userinfo['id']}\n";
        echo "昵称: {$userinfo['nickname']}\n";
        echo "等级ID: {$userinfo['levelid']}\n";
        echo "上级ID: {$userinfo['pid']}\n";
        echo "路径: {$userinfo['path']}\n";
    } else {
        echo "❌ 用户不存在！\n";
        exit;
    }
    echo "\n";

    // 2. 清除相关缓存
    echo "2. 清除缓存:\n";
    $cache_key = "getteammids_{$aid}_{$mid}_{$deep}_" . md5(implode(',', $levelids));
    echo "缓存键: {$cache_key}\n";
    $cache_result = cache($cache_key);
    if ($cache_result !== false) {
        echo "发现缓存数据，成员数量: " . count($cache_result) . "\n";
        cache($cache_key, null); // 清除缓存
        echo "✅ 缓存已清除\n";
    } else {
        echo "没有找到缓存数据\n";
    }
    echo "\n";

    // 3. 检查直接下级
    echo "3. 检查直接下级:\n";
    $direct_children = Db::name('member')
        ->where('aid', $aid)
        ->where('pid', $mid)
        ->select()
        ->toArray();
    
    echo "直接下级总数: " . count($direct_children) . "\n";
    if (count($direct_children) > 0) {
        echo "前5个直接下级:\n";
        foreach (array_slice($direct_children, 0, 5) as $child) {
            $level_match = in_array($child['levelid'], $levelids) ? '✅' : '❌';
            echo "- ID: {$child['id']}, 昵称: {$child['nickname']}, 等级ID: {$child['levelid']} {$level_match}\n";
        }
        
        // 检查符合等级条件的直接下级
        $filtered_direct = Db::name('member')
            ->where('aid', $aid)
            ->where('pid', $mid)
            ->where('levelid', 'in', $levelids)
            ->count();
        echo "符合等级条件的直接下级数量: {$filtered_direct}\n";
    }
    echo "\n";

    // 4. 测试优化查询方法
    echo "4. 测试优化查询方法:\n";
    $levelids_str = implode(',', $levelids);
    echo "查询SQL预览:\n";
    echo "SELECT id,path FROM ddwx_member WHERE aid={$aid} AND levelid IN ({$levelids_str}) AND find_in_set({$mid},path)\n";
    
    $members = Db::name('member')
        ->field('id,path,nickname,levelid')
        ->where('aid', $aid)
        ->where('levelid', 'in', $levelids_str)
        ->where('find_in_set(' . $mid . ',path)')
        ->select()
        ->toArray();
    
    echo "优化查询结果数量: " . count($members) . "\n";
    if (count($members) > 0) {
        echo "前5个查询结果:\n";
        foreach (array_slice($members, 0, 5) as $member) {
            echo "- ID: {$member['id']}, 昵称: {$member['nickname']}, 等级ID: {$member['levelid']}, 路径: {$member['path']}\n";
        }
    }
    echo "\n";

    // 5. 测试path字段查询
    echo "5. 测试path字段查询:\n";
    $path_members = Db::name('member')
        ->field('id,path,nickname,levelid')
        ->where('aid', $aid)
        ->where('path', 'like', "%,{$mid},%")
        ->limit(10)
        ->select()
        ->toArray();
    
    echo "path LIKE查询结果数量: " . count($path_members) . "\n";
    if (count($path_members) > 0) {
        echo "前5个path查询结果:\n";
        foreach (array_slice($path_members, 0, 5) as $member) {
            $level_match = in_array($member['levelid'], $levelids) ? '✅' : '❌';
            echo "- ID: {$member['id']}, 昵称: {$member['nickname']}, 等级ID: {$member['levelid']} {$level_match}, 路径: {$member['path']}\n";
        }
    }
    echo "\n";

    // 6. 检查所有下级的等级分布
    echo "6. 检查所有下级的等级分布:\n";
    $level_distribution = Db::name('member')
        ->field('levelid, count(*) as count')
        ->where('aid', $aid)
        ->where('path', 'like', "%,{$mid},%")
        ->group('levelid')
        ->select()
        ->toArray();
    
    echo "等级分布:\n";
    foreach ($level_distribution as $dist) {
        $in_filter = in_array($dist['levelid'], $levelids) ? '✅' : '❌';
        echo "- 等级ID: {$dist['levelid']}, 人数: {$dist['count']} {$in_filter}\n";
    }
    echo "\n";

    // 7. 手动执行getteammids逻辑
    echo "7. 手动执行getteammids逻辑:\n";
    
    // 模拟优化查询
    $result = [];
    foreach ($members as $member) {
        if ($deep > 0) {
            // 检查层级深度
            $path = explode(',', $member['path']);
            $path = array_reverse($path);
            $key = array_search($mid, $path);

            if ($key !== false && $key < $deep) {
                $result[] = $member['id'];
            }
        } else {
            $result[] = $member['id'];
        }
    }
    
    echo "手动执行结果数量: " . count($result) . "\n";
    if (count($result) > 0) {
        echo "前10个结果: " . implode(',', array_slice($result, 0, 10)) . "\n";
    }
    echo "\n";

    // 8. 问题诊断
    echo "8. 问题诊断:\n";
    
    if (count($direct_children) == 0) {
        echo "❌ 问题：用户没有直接下级\n";
    } elseif (count($members) == 0) {
        echo "❌ 问题：find_in_set查询没有结果\n";
        echo "可能原因：\n";
        echo "1. path字段格式不正确\n";
        echo "2. find_in_set函数使用有问题\n";
        echo "3. 等级筛选过于严格\n";
    } elseif (count($result) == 0) {
        echo "❌ 问题：层级深度检查有问题\n";
        echo "可能原因：\n";
        echo "1. path字段解析有问题\n";
        echo "2. 层级深度计算有误\n";
    } else {
        echo "✅ 手动执行正常，可能是缓存问题\n";
    }

    // 9. 建议解决方案
    echo "\n9. 建议解决方案:\n";
    echo "1. 清除所有相关缓存\n";
    echo "2. 检查path字段的数据完整性\n";
    echo "3. 考虑使用递归查询替代优化查询\n";
    echo "4. 添加更详细的调试日志\n";

} catch (Exception $e) {
    echo "❌ 执行过程中出错: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}

echo "\n=== 调试完成 ===\n";
?>
