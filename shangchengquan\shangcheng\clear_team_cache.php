<?php
/**
 * 清除团队成员缓存脚本
 * 用于解决getteammids缓存错误结果的问题
 */

// 引入框架
require_once 'vendor/autoload.php';
use think\facade\Cache;

echo "=== 清除团队成员缓存脚本 ===\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 清除所有getteammids相关的缓存
    echo "1. 清除getteammids缓存:\n";
    
    // 方法1：清除特定用户的缓存
    $aid = 110;
    $mid = 4500;
    $deep = 999;
    $levelids = [279,280,287,288,289,290];
    
    $cache_key = "getteammids_{$aid}_{$mid}_{$deep}_" . md5(implode(',', $levelids));
    echo "清除缓存键: {$cache_key}\n";
    
    $result = cache($cache_key, null);
    echo "✅ 特定缓存已清除\n";
    
    // 方法2：清除所有相关缓存（模糊匹配）
    echo "\n2. 清除所有相关缓存:\n";
    
    // 获取所有缓存键（如果支持的话）
    try {
        // 尝试清除所有以getteammids开头的缓存
        $pattern = "getteammids_*";
        echo "尝试清除模式: {$pattern}\n";
        
        // 由于不同的缓存驱动支持不同的清除方式，我们尝试多种方法
        Cache::clear();
        echo "✅ 所有缓存已清除\n";
        
    } catch (Exception $e) {
        echo "⚠️ 批量清除失败: " . $e->getMessage() . "\n";
        echo "建议手动重启缓存服务\n";
    }
    
    echo "\n3. 验证缓存清除结果:\n";
    $test_result = cache($cache_key);
    if ($test_result === false) {
        echo "✅ 缓存确认已清除\n";
    } else {
        echo "❌ 缓存仍然存在，可能需要重启缓存服务\n";
    }
    
    echo "\n=== 缓存清除完成 ===\n";
    echo "请重新调用销售统计接口测试\n";
    
} catch (Exception $e) {
    echo "❌ 执行过程中出错: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
?>
