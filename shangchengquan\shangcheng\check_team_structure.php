<?php
/**
 * 检查用户团队结构脚本
 * 用于诊断为什么getteammids返回0个成员
 */

// 引入框架
require_once 'vendor/autoload.php';
use think\facade\Db;

// 配置数据库连接
$config = [
    'hostname' => 'localhost',
    'database' => 'qixian_zhongheng',
    'username' => 'root',
    'password' => 'root123',
    'hostport' => '3306',
    'prefix'   => 'ddwx_',
    'charset'  => 'utf8',
];

// 初始化数据库
Db::setConfig($config);

echo "=== 用户团队结构检查脚本 ===\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n\n";

// 从日志中获取的参数
$aid = 110; // 应用ID
$test_mid = 4500; // 用户ID

echo "检查用户: aid={$aid}, mid={$test_mid}\n\n";

try {
    // 1. 检查用户基本信息
    echo "1. 用户基本信息:\n";
    $userinfo = Db::name('member')->where('aid', $aid)->where('id', $test_mid)->find();
    if ($userinfo) {
        echo "用户ID: {$userinfo['id']}\n";
        echo "昵称: {$userinfo['nickname']}\n";
        echo "等级ID: {$userinfo['levelid']}\n";
        echo "上级ID: {$userinfo['pid']}\n";
        echo "路径: {$userinfo['path']}\n";
        echo "注册时间: " . date('Y-m-d H:i:s', $userinfo['createtime']) . "\n";
    } else {
        echo "❌ 用户不存在！\n";
        exit;
    }
    echo "\n";

    // 2. 检查直接下级
    echo "2. 直接下级成员:\n";
    $direct_children = Db::name('member')
        ->where('aid', $aid)
        ->where('pid', $test_mid)
        ->select()
        ->toArray();
    
    if (empty($direct_children)) {
        echo "❌ 该用户没有直接下级成员！\n";
        echo "这就是为什么团队成员数量为0的原因。\n";
    } else {
        echo "找到 " . count($direct_children) . " 个直接下级:\n";
        foreach ($direct_children as $child) {
            echo "- ID: {$child['id']}, 昵称: {$child['nickname']}, 等级ID: {$child['levelid']}\n";
        }
    }
    echo "\n";

    // 3. 检查等级筛选条件
    echo "3. 等级筛选分析:\n";
    $levelids = [279,280,287,288,289,290]; // 从日志中获取
    echo "筛选的等级IDs: " . implode(',', $levelids) . "\n";
    
    if (!empty($direct_children)) {
        echo "直接下级的等级ID分布:\n";
        foreach ($direct_children as $child) {
            $in_filter = in_array($child['levelid'], $levelids) ? '✅' : '❌';
            echo "- 用户 {$child['id']} 等级ID: {$child['levelid']} {$in_filter}\n";
        }
        
        // 检查符合条件的下级
        $filtered_children = Db::name('member')
            ->where('aid', $aid)
            ->where('pid', $test_mid)
            ->where('levelid', 'in', $levelids)
            ->select()
            ->toArray();
        
        echo "符合等级条件的直接下级数量: " . count($filtered_children) . "\n";
    }
    echo "\n";

    // 4. 检查所有下级（不限层级）
    echo "4. 所有下级成员（不限层级）:\n";
    $all_children = Db::name('member')
        ->where('aid', $aid)
        ->where('path', 'like', "%,{$test_mid},%")
        ->select()
        ->toArray();
    
    if (empty($all_children)) {
        echo "❌ 该用户在整个团队中都没有下级成员！\n";
    } else {
        echo "找到 " . count($all_children) . " 个下级成员（所有层级）:\n";
        foreach ($all_children as $child) {
            $level_match = in_array($child['levelid'], $levelids) ? '✅' : '❌';
            echo "- ID: {$child['id']}, 昵称: {$child['nickname']}, 等级ID: {$child['levelid']} {$level_match}, 路径: {$child['path']}\n";
        }
        
        // 检查符合等级条件的所有下级
        $filtered_all = Db::name('member')
            ->where('aid', $aid)
            ->where('path', 'like', "%,{$test_mid},%")
            ->where('levelid', 'in', $levelids)
            ->select()
            ->toArray();
        
        echo "符合等级条件的所有下级数量: " . count($filtered_all) . "\n";
    }
    echo "\n";

    // 5. 检查用户自己的订单
    echo "5. 用户自己的订单情况:\n";
    $user_orders = Db::name('shop_order_goods')
        ->where('aid', $aid)
        ->where('mid', $test_mid)
        ->where('status', 'in', [1,2,3])
        ->select()
        ->toArray();
    
    if (empty($user_orders)) {
        echo "❌ 用户自己也没有任何有效订单！\n";
        echo "这解释了为什么销售件数为0。\n";
    } else {
        echo "用户自己有 " . count($user_orders) . " 个有效订单:\n";
        $total_num = 0;
        foreach ($user_orders as $order) {
            echo "- 订单ID: {$order['id']}, 商品: {$order['name']}, 数量: {$order['num']}, 状态: {$order['status']}\n";
            $total_num += $order['num'];
        }
        echo "总销售件数: {$total_num}\n";
    }
    echo "\n";

    // 6. 建议解决方案
    echo "6. 问题诊断和建议:\n";
    
    if (empty($direct_children) && empty($all_children)) {
        echo "❌ 问题原因：该用户没有任何下级团队成员\n";
        echo "解决方案：\n";
        echo "1. 如果这是正常情况（用户确实没有团队），那么显示0是正确的\n";
        echo "2. 如果应该有团队数据，请检查：\n";
        echo "   - 用户推荐关系是否正确设置（pid字段）\n";
        echo "   - 用户路径是否正确更新（path字段）\n";
        echo "   - 是否有数据导入问题\n";
    } elseif (!empty($all_children) && empty($filtered_children)) {
        echo "❌ 问题原因：用户有下级，但下级的等级ID不符合筛选条件\n";
        echo "解决方案：\n";
        echo "1. 检查等级筛选条件是否过于严格\n";
        echo "2. 考虑调整等级筛选逻辑\n";
        echo "3. 检查下级用户的等级设置是否正确\n";
    }
    
    if (empty($user_orders)) {
        echo "❌ 问题原因：用户自己也没有订单数据\n";
        echo "解决方案：\n";
        echo "1. 检查订单数据是否正确导入\n";
        echo "2. 检查订单状态是否正确（应该是1、2、3）\n";
        echo "3. 确认用户是否真的有购买行为\n";
    }

} catch (Exception $e) {
    echo "❌ 执行过程中出错: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}

echo "\n=== 检查完成 ===\n";
?>
